import React, { useEffect, useState } from "react";
import { CartSidebar } from "@/components/CartSidebar";
import { useCart } from "@/context/CartContext";
import { toast } from "@/components/ui/use-toast";

const categories = [
    "Milking Machines",
    "Chaff Cutters",
    "Feed Grinders",
    "Bulk Milk Chillers",
    "TMR Wagons",
    "Pasteurizers",
    "Other",
];
const conditions = ["New", "Used"];

const mockBrands = ["DeLaval", "GEA", "Keventer", "Vijay", "Local Brand"];

const defaultForm = {
    name: "",
    category: "",
    brand: "",
    condition: "New",
    capacity: "",
    location: "",
    price: 0,
    description: "",
    images: [],
    seller: "",
    contactInfo: "",
    conditionReport: "",
};

const fallbackEquipment = [
    {
        _id: '1',
        name: "Milking Machine Pro",
        category: "Milking Machines",
        brand: "DeLaval",
        condition: "New",
        capacity: "500L/hr",
        location: "Hyderabad",
        price: 120000,
        description: "High-efficiency milking machine for medium to large dairy farms.",
        images: ["data:image/jpeg;base64,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"],
        seller: "AgroTech Solutions",
        contactInfo: "<EMAIL>",
        conditionReport: ""
    },
    {
        _id: '2',
        name: "Feed Grinder X2",
        category: "Feed Grinders",
        brand: "GEA",
        condition: "Used",
        capacity: "200kg/hr",
        location: "Bangalore",
        price: 45000,
        description: "Durable feed grinder, lightly used, well maintained.",
        images: ["data:image/jpeg;base64,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"],
        seller: "FarmEquip Dealer",
        contactInfo: "<EMAIL>",
        conditionReport: "Minor scratches, fully functional."
    },
    {
        _id: '3',
        name: "Bulk Milk Chiller 1000L",
        category: "Bulk Milk Chillers",
        brand: "Keventer",
        condition: "New",
        capacity: "1000L",
        location: "Vijayawada",
        price: 250000,
        description: "Brand new milk chiller with rapid cooling technology.",
        images: ["data:image/jpeg;base64,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"],
        seller: "DairyMart",
        contactInfo: "<EMAIL>",
        conditionReport: ""
    },
    {
        _id: '4',
        name: "TMR Wagon 3000",
        category: "TMR Wagons",
        brand: "Vijay",
        condition: "Used",
        capacity: "3 tons",
        location: "Nagpur",
        price: 180000,
        description: "Used TMR wagon, perfect for mixing and distributing feed.",
        images: ["data:image/jpeg;base64,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"],
        seller: "FeedMix Solutions",
        contactInfo: "<EMAIL>",
        conditionReport: "Serviced last month, minor dents."
    },
    {
        _id: '5',
        name: "Pasteurizer Compact",
        category: "Pasteurizers",
        brand: "Local Brand",
        condition: "New",
        capacity: "200L/batch",
        location: "Chennai",
        price: 95000,
        description: "Compact pasteurizer for small-scale dairy operations.",
        images: ["data:image/jpeg;base64,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"],
        seller: "Dairy Essentials",
        contactInfo: "<EMAIL>",
        conditionReport: ""
    },
    {
        _id: '6',
        name: "Chaff Cutter Supreme",
        category: "Chaff Cutters",
        brand: "Keventer",
        condition: "Used",
        capacity: "1 ton/hr",
        location: "Patna",
        price: 30000,
        description: "Heavy-duty chaff cutter, ideal for large herds.",
        images: ["data:image/jpeg;base64,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"],
        seller: "GreenFarms",
        contactInfo: "<EMAIL>",
        conditionReport: "Blades replaced recently."
    },
    {
        _id: '7',
        name: "Multi-Utility Farm Equipment",
        category: "Other",
        brand: "Local Brand",
        condition: "New",
        capacity: "N/A",
        location: "Lucknow",
        price: 40000,
        description: "Versatile equipment for various farm tasks.",
        images: ["https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSNf2dpI_L0SI57M4GSpImAypa5XBRUOwZzlQ&s"],
        seller: "Farmers Hub",
        contactInfo: "<EMAIL>",
        conditionReport: ""
    },
    {
        _id: '8',
        name: "Bulk Milk Chiller 500L",
        category: "Bulk Milk Chillers",
        brand: "DeLaval",
        condition: "Used",
        capacity: "500L",
        location: "Indore",
        price: 120000,
        description: "Reliable used milk chiller, energy efficient.",
        images: ["data:image/jpeg;base64,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"],
        seller: "Dairy Solutions",
        contactInfo: "<EMAIL>",
        conditionReport: "Compressor serviced, minor rust spots."
    },
    {
        _id: '9',
        name: "Automatic Milking System",
        category: "Milking Machines",
        brand: "GEA",
        condition: "New",
        capacity: "700L/hr",
        location: "Pune",
        price: 200000,
        description: "State-of-the-art automatic milking system for large farms.",
        images: ["data:image/jpeg;base64,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"],
        seller: "Agri Innovations",
        contactInfo: "<EMAIL>",
        conditionReport: ""
    }
];

const PAGE_SIZE = 6;

const EquipmentMart = () => {
    const [equipment, setEquipment] = useState<any[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState("");
    const [filters, setFilters] = useState({
        category: categories[0],
        condition: "New",
        brand: mockBrands[0],
        location: "",
    });
    const [showAdd, setShowAdd] = useState(false);
    const [form, setForm] = useState<any>(defaultForm);
    const [formImages, setFormImages] = useState<File[]>([]);
    const [formLoading, setFormLoading] = useState(false);
    const [formError, setFormError] = useState("");
    const [selected, setSelected] = useState<any | null>(null);
    const [showProfile, setShowProfile] = useState(false);
    const [search, setSearch] = useState("");
    const [priceRange, setPriceRange] = useState<[number, number]>([0, 1000000]);
    const [capacity, setCapacity] = useState("");

    // Filter, search, and sort state
    const [filterCategory, setFilterCategory] = useState<string>("");
    const [filterBrand, setFilterBrand] = useState<string>("");
    const [filterCondition, setFilterCondition] = useState<string>("");
    const [searchTerm, setSearchTerm] = useState<string>("");
    const [sortBy, setSortBy] = useState<string>("newest");

    // Pagination state
    const [tab, setTab] = useState<'buy' | 'sell'>('buy');
    const [currentPage, setCurrentPage] = useState(1);
    const [editId, setEditId] = useState<string | null>(null);
    const [editEquipment, setEditEquipment] = useState<any>(null);

    const { addToCart } = useCart();
    const [cartOpen, setCartOpen] = useState(false);

    // Filtering and sorting logic
    const getFilteredSortedEquipment = (data: any[]) => {
        let filtered = data;
        if (filterCategory) filtered = filtered.filter(eq => eq.category === filterCategory);
        if (filterBrand) filtered = filtered.filter(eq => eq.brand === filterBrand);
        if (filterCondition) filtered = filtered.filter(eq => eq.condition === filterCondition);
        if (searchTerm) {
            const term = searchTerm.toLowerCase();
            filtered = filtered.filter(eq =>
                eq.name.toLowerCase().includes(term) ||
                eq.location.toLowerCase().includes(term)
            );
        }
        if (sortBy === "price-asc") filtered = filtered.sort((a, b) => a.price - b.price);
        else if (sortBy === "price-desc") filtered = filtered.sort((a, b) => b.price - a.price);
        else if (sortBy === "newest") filtered = filtered.sort((a, b) => (b.createdAt || 0) - (a.createdAt || 0));
        return filtered;
    };

    // Fetch equipment listings
    const fetchEquipment = () => {
        setLoading(true);
        fetch("/api/equipment")
            .then(res => {
                if (!res.ok) throw new Error("Failed to fetch equipment");
                return res.json();
            })
            .then(data => {
                setEquipment(data);
                setLoading(false);
            })
            .catch(err => {
                setError(err.message);
                setLoading(false);
            });
    };

    useEffect(() => {
        fetchEquipment();
    }, []);

    // Profile modal
    const openProfile = (eq: any) => {
        setSelected(eq);
        setShowProfile(true);
    };
    const closeProfile = () => {
        setShowProfile(false);
        setSelected(null);
    };

    // Financing partners (mock)
    const partners = [
        { name: "AgriBank", logo: "/dairy-lift-logo.png", url: "#" },
        { name: "NBFC Pro", logo: "/dairy-lift-logo.png", url: "#" },
        { name: "Farm Finance", logo: "/dairy-lift-logo.png", url: "#" },
    ];

    const [showAddModal, setShowAddModal] = useState(false);
    const [newEquipment, setNewEquipment] = useState<any>({
        name: "",
        category: categories[0],
        brand: mockBrands[0],
        condition: conditions[0],
        capacity: "",
        location: "",
        price: 0,
        description: "",
        images: [""],
        seller: "",
        contactInfo: "",
        conditionReport: ""
    });
    const [localEquipment, setLocalEquipment] = useState<any[]>([]);

    // Add equipment handler (frontend only)
    const handleAddEquipment = () => {
        const eq = {
            ...newEquipment,
            _id: Date.now().toString(),
            images: newEquipment.images.filter((img: string) => img),
        };
        setLocalEquipment(prev => [eq, ...prev]);
        setShowAddModal(false);
        setNewEquipment({
            name: "",
            category: categories[0],
            brand: mockBrands[0],
            condition: conditions[0],
            capacity: "",
            location: "",
            price: 0,
            description: "",
            images: [""],
            seller: "",
            contactInfo: "",
            conditionReport: ""
        });
    };

    // Edit equipment handler (frontend only)
    const handleEditEquipment = (eq: any) => {
        setEditId(eq._id);
        setEditEquipment({ ...eq });
        setShowAddModal(true);
    };
    const handleUpdateEquipment = () => {
        setLocalEquipment(prev => prev.map(eq => eq._id === editId ? { ...editEquipment, _id: editId } : eq));
        setShowAddModal(false);
        setEditId(null);
        setEditEquipment(null);
    };
    const handleDeleteEquipment = (id: string) => {
        setLocalEquipment(prev => prev.filter(eq => eq._id !== id));
    };

    // Pagination logic
    const paginatedEquipment = (data: any[]) => {
        const start = (currentPage - 1) * PAGE_SIZE;
        return data.slice(start, start + PAGE_SIZE);
    };
    const totalPages = (data: any[]) => Math.ceil(data.length / PAGE_SIZE);

    // Add to cart handler
    const handleAddToCart = (eq: any) => {
        addToCart(eq._id, eq.name, eq.price, eq.images?.[0] || '', eq.category, 1);
        toast({ title: "Added to Cart", description: `${eq.name} added to your cart.` });
    };
    // Buy now handler
    const handleBuyNow = (eq: any) => {
        addToCart(eq._id, eq.name, eq.price, eq.images?.[0] || '', eq.category, 1);
        setCartOpen(true);
        toast({ title: "Buy Now", description: `Proceed to checkout for ${eq.name}.` });
    };

    return (
        <div className="w-full min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-emerald-50 pb-16">
            {/* Hero */}
            <section className="relative w-full h-64 flex items-center justify-center mb-10">
                <img src="https://images.unsplash.com/photo-1519125323398-675f0ddb6308?auto=format&fit=crop&w=1200&q=80" alt="Equipment Hero" className="absolute inset-0 w-full h-full object-cover brightness-75" />
                <div className="relative z-10 text-center">
                    <h1 className="text-4xl md:text-5xl font-extrabold bg-gradient-to-r from-emerald-400 via-blue-500 to-purple-600 bg-clip-text text-transparent drop-shadow-lg animate-fadeInUp">Equipment Mart</h1>
                    <p className="mt-4 text-lg md:text-2xl text-white/90 font-medium animate-fadeInUp delay-100">A marketplace for new and used dairy farming machinery.</p>
                </div>
            </section>

            {/* Removed filter section as per user request */}
            <div className="max-w-6xl mx-auto flex justify-end mb-8">
                <a href="/dairy-lift/livestock-market" className="rounded-xl bg-gradient-to-r from-emerald-400 to-blue-400 text-white font-bold px-6 py-3 shadow hover:scale-105 transition-all duration-200">View Livestock Market</a>
            </div>

            {/* Buy/Sell Tabs */}
            <div className="max-w-6xl mx-auto flex justify-center mt-4 mb-6">
                <div className="flex rounded-full bg-blue-100 p-1 shadow-inner">
                    <button onClick={() => setTab('buy')} className={`px-6 py-2 rounded-full font-bold transition-all duration-200 ${tab === 'buy' ? 'bg-blue-500 text-white shadow' : 'text-blue-700 hover:bg-blue-200'}`}>Buy Equipment</button>
                    <button onClick={() => setTab('sell')} className={`px-6 py-2 rounded-full font-bold transition-all duration-200 ${tab === 'sell' ? 'bg-emerald-500 text-white shadow' : 'text-emerald-700 hover:bg-emerald-100'}`}>Sell Equipment</button>
                </div>
            </div>

            {/* BUY TAB */}
            {tab === 'buy' && (
                <>
                    {/* Filter & Sort Bar */}
                    <div className="max-w-6xl mx-auto flex flex-wrap gap-4 items-center justify-between mb-8 px-4">
                        <div className="flex flex-wrap gap-2 items-center">
                            <select value={filterCategory} onChange={e => { setFilterCategory(e.target.value); setCurrentPage(1); }} className="rounded-lg border px-3 py-2">
                                <option value="">All Categories</option>
                                {categories.map(cat => <option key={cat} value={cat}>{cat}</option>)}
                            </select>
                            <select value={filterBrand} onChange={e => { setFilterBrand(e.target.value); setCurrentPage(1); }} className="rounded-lg border px-3 py-2">
                                <option value="">All Brands</option>
                                {mockBrands.map(brand => <option key={brand} value={brand}>{brand}</option>)}
                            </select>
                            <select value={filterCondition} onChange={e => { setFilterCondition(e.target.value); setCurrentPage(1); }} className="rounded-lg border px-3 py-2">
                                <option value="">All Conditions</option>
                                {conditions.map(cond => <option key={cond} value={cond}>{cond}</option>)}
                            </select>
                            <input
                                type="text"
                                placeholder="Search by name or location..."
                                value={searchTerm}
                                onChange={e => { setSearchTerm(e.target.value); setCurrentPage(1); }}
                                className="rounded-lg border px-3 py-2"
                            />
                        </div>
                        <div className="flex gap-2 items-center">
                            <label className="font-medium">Sort by:</label>
                            <select value={sortBy} onChange={e => setSortBy(e.target.value)} className="rounded-lg border px-3 py-2">
                                <option value="newest">Newest</option>
                                <option value="price-asc">Price: Low to High</option>
                                <option value="price-desc">Price: High to Low</option>
                            </select>
                        </div>
                    </div>
                    {/* Equipment Listings */}
                    <section className="max-w-6xl mx-auto grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-10 mb-8 px-4 animate-fadeInUp">
                        {loading && <div className="col-span-full text-center text-blue-600 font-bold animate-pulse">Loading equipment...</div>}
                        {error && <div className="col-span-full text-center text-red-600 font-bold animate-fadeIn">{error}</div>}
                        {!loading && !error && equipment.length === 0 && (
                            <>
                                <div className="col-span-full text-center text-gray-500 animate-fadeIn">No equipment found from backend. Showing sample equipment:</div>
                                {getFilteredSortedEquipment([...localEquipment, ...fallbackEquipment]).length === 0 ? (
                                    <div className="col-span-full text-center text-gray-400">No equipment matches your filters.</div>
                                ) : (
                                    paginatedEquipment(getFilteredSortedEquipment([...localEquipment, ...fallbackEquipment])).map(eq => (
                                        <div key={eq._id} className="bg-white/70 backdrop-blur-lg rounded-3xl shadow-xl p-6 flex flex-col items-center border border-blue-100 hover:shadow-2xl hover:scale-105 transition-all duration-300 group relative overflow-hidden animate-fadeInUp">
                                            <div className="relative w-44 h-44 mb-4">
                                                {eq.images && eq.images.length > 0 ? (
                                                    <img src={eq.images[0]} alt={eq.name} className="w-44 h-44 object-cover rounded-2xl shadow-md group-hover:scale-110 transition-transform duration-300" />
                                                ) : (
                                                    <img src="https://via.placeholder.com/160x160?text=No+Image" alt={eq.name} className="w-44 h-44 object-cover rounded-2xl shadow-md" />
                                                )}
                                            </div>
                                            <h3 className="text-2xl font-bold text-blue-800 mb-1 group-hover:text-emerald-600 transition-colors duration-200">{eq.name}</h3>
                                            <div className="text-gray-700 mb-1">Category: <span className="font-semibold">{eq.category}</span></div>
                                            <div className="text-gray-700 mb-1">Brand: <span className="font-semibold">{eq.brand}</span></div>
                                            <div className="text-gray-700 mb-1">Condition: <span className="font-semibold">{eq.condition}</span></div>
                                            <div className="text-gray-700 mb-1">Capacity: <span className="font-semibold">{eq.capacity}</span></div>
                                            <div className="text-gray-700 mb-1">Location: <span className="font-semibold">{eq.location}</span></div>
                                            <div className="text-gray-700 mb-1">Price: <span className="font-semibold">₹{eq.price}</span></div>
                                            <div className="text-gray-700 mb-1">Seller: <span className="font-semibold">{eq.seller}</span></div>
                                            <div className="mt-2 flex gap-2">
                                                <button onClick={() => handleAddToCart(eq)} className="mt-2 bg-gradient-to-r from-emerald-500 to-blue-500 text-white font-bold py-2 px-4 rounded-full shadow hover:scale-105 transition-all duration-200 mr-2">Add to Cart</button>
                                                <button onClick={() => handleBuyNow(eq)} className="mt-2 bg-gradient-to-r from-blue-500 to-emerald-500 text-white font-bold py-2 px-4 rounded-full shadow hover:scale-105 transition-all duration-200">Buy Now</button>
                                            </div>
                                            <button onClick={() => openProfile(eq)} className="mt-4 bg-gradient-to-r from-blue-500 via-emerald-500 to-cyan-500 text-white font-bold py-2 px-6 rounded-full shadow hover:scale-105 transition-all duration-200">View Details</button>
                                        </div>
                                    ))
                                )}
                            </>
                        )}
                        {!loading && !error && equipment.length > 0 && (
                            getFilteredSortedEquipment(equipment).length === 0 ? (
                                <div className="col-span-full text-center text-gray-400">No equipment matches your filters.</div>
                            ) : (
                                paginatedEquipment(getFilteredSortedEquipment(equipment)).map(eq => (
                                    <div key={eq._id} className="bg-white/70 backdrop-blur-lg rounded-3xl shadow-xl p-6 flex flex-col items-center border border-blue-100 hover:shadow-2xl hover:scale-105 transition-all duration-300 group relative overflow-hidden animate-fadeInUp">
                                        <div className="relative w-44 h-44 mb-4">
                                            {eq.images && eq.images.length > 0 ? (
                                                <img src={eq.images[0]} alt={eq.name} className="w-44 h-44 object-cover rounded-2xl shadow-md group-hover:scale-110 transition-transform duration-300" />
                                            ) : (
                                                <img src="https://via.placeholder.com/160x160?text=No+Image" alt={eq.name} className="w-44 h-44 object-cover rounded-2xl shadow-md" />
                                            )}
                                        </div>
                                        <h3 className="text-2xl font-bold text-blue-800 mb-1 group-hover:text-emerald-600 transition-colors duration-200">{eq.name}</h3>
                                        <div className="text-gray-700 mb-1">Category: <span className="font-semibold">{eq.category}</span></div>
                                        <div className="text-gray-700 mb-1">Brand: <span className="font-semibold">{eq.brand}</span></div>
                                        <div className="text-gray-700 mb-1">Condition: <span className="font-semibold">{eq.condition}</span></div>
                                        <div className="text-gray-700 mb-1">Capacity: <span className="font-semibold">{eq.capacity}</span></div>
                                        <div className="text-gray-700 mb-1">Location: <span className="font-semibold">{eq.location}</span></div>
                                        <div className="text-gray-700 mb-1">Price: <span className="font-semibold">₹{eq.price}</span></div>
                                        <div className="text-gray-700 mb-1">Seller: <span className="font-semibold">{eq.seller}</span></div>
                                        <div className="mt-2 flex gap-2">
                                            <button onClick={() => handleAddToCart(eq)} className="mt-2 bg-gradient-to-r from-emerald-500 to-blue-500 text-white font-bold py-2 px-4 rounded-full shadow hover:scale-105 transition-all duration-200 mr-2">Add to Cart</button>
                                            <button onClick={() => handleBuyNow(eq)} className="mt-2 bg-gradient-to-r from-blue-500 to-emerald-500 text-white font-bold py-2 px-4 rounded-full shadow hover:scale-105 transition-all duration-200">Buy Now</button>
                                        </div>
                                        <button onClick={() => openProfile(eq)} className="mt-4 bg-gradient-to-r from-blue-500 via-emerald-500 to-cyan-500 text-white font-bold py-2 px-6 rounded-full shadow hover:scale-105 transition-all duration-200">View Details</button>
                                    </div>
                                ))
                            )
                        )}
                    </section>
                    {/* Pagination */}
                    <div className="max-w-6xl mx-auto flex justify-center mb-12">
                        {getFilteredSortedEquipment(equipment.length > 0 ? equipment : [...localEquipment, ...fallbackEquipment]).length > PAGE_SIZE && (
                            <div className="flex gap-2">
                                {Array.from({ length: totalPages(getFilteredSortedEquipment(equipment.length > 0 ? equipment : [...localEquipment, ...fallbackEquipment])) }, (_, i) => (
                                    <button
                                        key={i}
                                        onClick={() => setCurrentPage(i + 1)}
                                        className={`px-4 py-2 rounded-full font-bold transition-all duration-200 ${currentPage === i + 1 ? 'bg-blue-500 text-white' : 'bg-blue-100 text-blue-700 hover:bg-blue-200'}`}
                                    >
                                        {i + 1}
                                    </button>
                                ))}
                            </div>
                        )}
                    </div>
                </>
            )}

            {/* SELL TAB */}
            {tab === 'sell' && (
                <>
                    {/* Add Equipment Button */}
                    <div className="max-w-6xl mx-auto flex justify-end mb-2 px-4">
                        <button onClick={() => { setShowAddModal(true); setEditId(null); setEditEquipment(null); }} className="rounded-xl bg-gradient-to-r from-blue-400 to-emerald-400 text-white font-bold px-6 py-2 shadow hover:scale-105 transition-all duration-200">Add Equipment</button>
                    </div>
                    {/* User's Equipment List */}
                    <section className="max-w-6xl mx-auto grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-10 mb-8 px-4 animate-fadeInUp">
                        {localEquipment.length === 0 ? (
                            <div className="col-span-full text-center text-gray-400">You have not added any equipment yet.</div>
                        ) : (
                            localEquipment.map(eq => (
                                <div key={eq._id} className="bg-white/80 backdrop-blur-lg rounded-3xl shadow-xl p-6 flex flex-col items-center border border-emerald-100 hover:shadow-2xl hover:scale-105 transition-all duration-300 group relative overflow-hidden animate-fadeInUp">
                                    <div className="relative w-44 h-44 mb-4">
                                        {eq.images && eq.images.length > 0 ? (
                                            <img src={eq.images[0]} alt={eq.name} className="w-44 h-44 object-cover rounded-2xl shadow-md group-hover:scale-110 transition-transform duration-300" />
                                        ) : (
                                            <img src="https://via.placeholder.com/160x160?text=No+Image" alt={eq.name} className="w-44 h-44 object-cover rounded-2xl shadow-md" />
                                        )}
                                    </div>
                                    <h3 className="text-2xl font-bold text-emerald-800 mb-1 group-hover:text-blue-600 transition-colors duration-200">{eq.name}</h3>
                                    <div className="text-gray-700 mb-1">Category: <span className="font-semibold">{eq.category}</span></div>
                                    <div className="text-gray-700 mb-1">Brand: <span className="font-semibold">{eq.brand}</span></div>
                                    <div className="text-gray-700 mb-1">Condition: <span className="font-semibold">{eq.condition}</span></div>
                                    <div className="text-gray-700 mb-1">Capacity: <span className="font-semibold">{eq.capacity}</span></div>
                                    <div className="text-gray-700 mb-1">Location: <span className="font-semibold">{eq.location}</span></div>
                                    <div className="text-gray-700 mb-1">Price: <span className="font-semibold">₹{eq.price}</span></div>
                                    <div className="text-gray-700 mb-1">Seller: <span className="font-semibold">{eq.seller}</span></div>
                                    <div className="flex gap-2 mt-4">
                                        <button onClick={() => handleEditEquipment(eq)} className="bg-blue-500 text-white px-4 py-2 rounded-full font-bold shadow hover:scale-105 transition-all duration-200">Edit</button>
                                        <button onClick={() => handleDeleteEquipment(eq._id)} className="bg-red-500 text-white px-4 py-2 rounded-full font-bold shadow hover:scale-105 transition-all duration-200">Delete</button>
                                    </div>
                                </div>
                            ))
                        )}
                    </section>
                </>
            )}

            {/* Equipment Profile Modal */}
            {showProfile && selected && (
                <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/40">
                    <div className="bg-white rounded-2xl shadow-2xl max-w-lg w-full p-8 relative animate-fadeIn">
                        <button onClick={closeProfile} className="absolute top-4 right-4 text-2xl text-blue-700 hover:text-blue-900">&times;</button>
                        <div className="flex flex-col items-center">
                            {selected.images && selected.images.length > 0 ? (
                                <div className="flex gap-2 mb-4 overflow-x-auto">
                                    {selected.images.map((img: string, idx: number) => (
                                        <img key={idx} src={img} alt={selected.name + ' ' + (idx + 1)} className="w-40 h-40 object-cover rounded-xl shadow-md" />
                                    ))}
                                </div>
                            ) : (
                                <img src="https://via.placeholder.com/160x160?text=No+Image" alt={selected.name} className="w-40 h-40 object-cover rounded-xl mb-4 shadow-md" />
                            )}
                            <h2 className="text-2xl font-bold text-blue-800 mb-2">{selected.name}</h2>
                            <div className="text-gray-700 mb-1">Category: <span className="font-semibold">{selected.category}</span></div>
                            <div className="text-gray-700 mb-1">Brand: <span className="font-semibold">{selected.brand}</span></div>
                            <div className="text-gray-700 mb-1">Condition: <span className="font-semibold">{selected.condition}</span></div>
                            <div className="text-gray-700 mb-1">Capacity: <span className="font-semibold">{selected.capacity}</span></div>
                            <div className="text-gray-700 mb-1">Location: <span className="font-semibold">{selected.location}</span></div>
                            <div className="text-gray-700 mb-1">Price: <span className="font-semibold">₹{selected.price}</span></div>
                            <div className="text-gray-700 mb-1">Seller: <span className="font-semibold">{selected.seller}</span></div>
                            <div className="text-gray-700 mb-1">Contact: <span className="font-semibold">{selected.contactInfo}</span></div>
                            {selected.condition === "Used" && selected.conditionReport && (
                                <div className="text-gray-700 mb-1">Condition Report: <span className="font-semibold">{selected.conditionReport}</span></div>
                            )}
                            <div className="text-gray-700 mb-1">Description: <span className="font-semibold">{selected.description}</span></div>
                            <div className="mt-6 w-full flex justify-center">
                                {selected.contactInfo ? (
                                    <a href={`mailto:${selected.contactInfo}`} className="bg-gradient-to-r from-blue-500 to-emerald-500 text-white font-bold py-3 px-8 rounded-full shadow-lg hover:scale-105 transition-all duration-200 text-lg">
                                        Contact Seller
                                    </a>
                                ) : (
                                    <span className="text-gray-500">No contact info available</span>
                                )}
                            </div>
                        </div>
                    </div>
                </div>
            )}

            {/* Financing Partners */}
            <section className="max-w-5xl mx-auto mt-16 bg-white/80 backdrop-blur rounded-2xl shadow-lg p-8 border border-blue-100 animate-fadeIn">
                <h2 className="text-2xl font-bold text-blue-800 mb-6 text-center">Financing Partners</h2>
                <div className="flex flex-wrap justify-center gap-8">
                    {partners.map(p => (
                        <a key={p.name} href={p.url} target="_blank" rel="noopener noreferrer" className="flex flex-col items-center bg-white rounded-xl shadow p-4 hover:scale-105 transition-all duration-200">
                            <img src={p.logo} alt={p.name} className="h-16 w-24 object-contain mb-2" />
                            <span className="font-semibold text-blue-700">{p.name}</span>
                        </a>
                    ))}
                </div>
            </section>

            {/* Add/Edit Equipment Modal */}
            {showAddModal && (
                <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/40">
                    <div className="bg-white rounded-2xl shadow-2xl max-w-lg w-full p-8 relative animate-fadeIn">
                        <button onClick={() => { setShowAddModal(false); setEditId(null); setEditEquipment(null); }} className="absolute top-4 right-4 text-2xl text-blue-700 hover:text-blue-900">&times;</button>
                        <h2 className="text-2xl font-bold text-blue-800 mb-4">{editId ? 'Edit Equipment' : 'Add Equipment'}</h2>
                        <div className="flex flex-col gap-3">
                            <input className="border rounded px-3 py-2" placeholder="Name" value={editId ? editEquipment.name : newEquipment.name} onChange={e => editId ? setEditEquipment({ ...editEquipment, name: e.target.value }) : setNewEquipment({ ...newEquipment, name: e.target.value })} />
                            <select className="border rounded px-3 py-2" value={editId ? editEquipment.category : newEquipment.category} onChange={e => editId ? setEditEquipment({ ...editEquipment, category: e.target.value }) : setNewEquipment({ ...newEquipment, category: e.target.value })}>
                                {categories.map(cat => <option key={cat} value={cat}>{cat}</option>)}
                            </select>
                            <select className="border rounded px-3 py-2" value={editId ? editEquipment.brand : newEquipment.brand} onChange={e => editId ? setEditEquipment({ ...editEquipment, brand: e.target.value }) : setNewEquipment({ ...newEquipment, brand: e.target.value })}>
                                {mockBrands.map(brand => <option key={brand} value={brand}>{brand}</option>)}
                            </select>
                            <select className="border rounded px-3 py-2" value={editId ? editEquipment.condition : newEquipment.condition} onChange={e => editId ? setEditEquipment({ ...editEquipment, condition: e.target.value }) : setNewEquipment({ ...newEquipment, condition: e.target.value })}>
                                {conditions.map(cond => <option key={cond} value={cond}>{cond}</option>)}
                            </select>
                            <input className="border rounded px-3 py-2" placeholder="Capacity" value={editId ? editEquipment.capacity : newEquipment.capacity} onChange={e => editId ? setEditEquipment({ ...editEquipment, capacity: e.target.value }) : setNewEquipment({ ...newEquipment, capacity: e.target.value })} />
                            <input className="border rounded px-3 py-2" placeholder="Location" value={editId ? editEquipment.location : newEquipment.location} onChange={e => editId ? setEditEquipment({ ...editEquipment, location: e.target.value }) : setNewEquipment({ ...newEquipment, location: e.target.value })} />
                            <input className="border rounded px-3 py-2" type="number" placeholder="Price" value={editId ? editEquipment.price : newEquipment.price} onChange={e => editId ? setEditEquipment({ ...editEquipment, price: Number(e.target.value) }) : setNewEquipment({ ...newEquipment, price: Number(e.target.value) })} />
                            <input className="border rounded px-3 py-2" placeholder="Seller" value={editId ? editEquipment.seller : newEquipment.seller} onChange={e => editId ? setEditEquipment({ ...editEquipment, seller: e.target.value }) : setNewEquipment({ ...newEquipment, seller: e.target.value })} />
                            <input className="border rounded px-3 py-2" placeholder="Contact Info (email)" value={editId ? editEquipment.contactInfo : newEquipment.contactInfo} onChange={e => editId ? setEditEquipment({ ...editEquipment, contactInfo: e.target.value }) : setNewEquipment({ ...newEquipment, contactInfo: e.target.value })} />
                            {(editId ? editEquipment.condition : newEquipment.condition) === "Used" && (
                                <input className="border rounded px-3 py-2" placeholder="Condition Report" value={editId ? editEquipment.conditionReport : newEquipment.conditionReport} onChange={e => editId ? setEditEquipment({ ...editEquipment, conditionReport: e.target.value }) : setNewEquipment({ ...newEquipment, conditionReport: e.target.value })} />
                            )}
                            <textarea className="border rounded px-3 py-2" placeholder="Description" value={editId ? editEquipment.description : newEquipment.description} onChange={e => editId ? setEditEquipment({ ...editEquipment, description: e.target.value }) : setNewEquipment({ ...newEquipment, description: e.target.value })} />
                            <input className="border rounded px-3 py-2" placeholder="Image URL(s), comma separated" value={(editId ? editEquipment.images : newEquipment.images).join(",")} onChange={e => editId ? setEditEquipment({ ...editEquipment, images: e.target.value.split(",").map((s: string) => s.trim()) }) : setNewEquipment({ ...newEquipment, images: e.target.value.split(",").map((s: string) => s.trim()) })} />
                            <button onClick={editId ? handleUpdateEquipment : handleAddEquipment} className="mt-2 bg-gradient-to-r from-blue-500 to-emerald-500 text-white font-bold py-2 px-6 rounded-full shadow hover:scale-105 transition-all duration-200">{editId ? 'Update' : 'Add'}</button>
                        </div>
                    </div>
                </div>
            )}

            <CartSidebar />
        </div>
    );
};

export default EquipmentMart; 